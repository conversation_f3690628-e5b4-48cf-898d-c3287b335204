/**
 * Comprehensive price parsing utility that handles various international price formats
 * 
 * Supported formats:
 * - Currency symbols before/after: $1,343.88, 1,343.88$, €1.343,88, £1,343.88, ₹1,34,388.00
 * - Currency codes: USD 1,343.88, 1,343.88 USD, EUR 1.343,88
 * - Different separators: comma decimal (1.343,88), dot decimal (1,343.88), space thousands (1 343,88), apostrophe (1'343.88)
 * - No decimal places: $1343, 1,343€, USD 1343
 * - Plain numbers: 1343.88, 1,343.88, 1.343,88, 1 343.88
 */

// Common currency symbols and codes
const CURRENCY_SYMBOLS = ['$', '€', '£', '¥', '₹', '₽', '₩', '₪', '₦', '₡', '₨', '₵', '₴', '₸', '₼', '₾', '₿'];
const CURRENCY_CODES = ['USD', 'EUR', 'GBP', 'JPY', 'INR', 'RUB', 'KRW', 'ILS', 'NGN', 'CRC', 'PKR', 'GHS', 'UAH', 'KZT', 'AZN', 'GEL', 'BTC'];

// Separators used in different locales
const THOUSANDS_SEPARATORS = [',', '.', ' ', "'"];  // Including space and apostrophe variants
const DECIMAL_SEPARATORS = ['.', ','];

export function parsePrice(priceText: string): string | null {
  if (!priceText || typeof priceText !== 'string') {
    return null;
  }

  // Clean the input - remove extra whitespace and normalize
  let cleanText = priceText.trim().replace(/\s+/g, ' ');
  
  if (!cleanText) {
    return null;
  }

  // Remove currency symbols and codes to isolate the numeric part
  const numericPart = extractNumericPart(cleanText);
  
  if (!numericPart) {
    return null;
  }

  // Parse the numeric part based on separator patterns
  const parsedNumber = parseNumericPart(numericPart);
  
  if (parsedNumber === null || isNaN(parsedNumber) || parsedNumber < 0) {
    return null;
  }

  // Return as string with proper decimal formatting
  return parsedNumber.toString();
}

function extractNumericPart(text: string): string | null {
  // Remove currency symbols from the beginning and end
  let cleaned = text;
  
  // Remove currency codes (3-letter codes like USD, EUR)
  for (const code of CURRENCY_CODES) {
    const codeRegex = new RegExp(`\\b${code}\\b`, 'gi');
    cleaned = cleaned.replace(codeRegex, '').trim();
  }
  
  // Remove currency symbols
  for (const symbol of CURRENCY_SYMBOLS) {
    // Escape special regex characters
    const escapedSymbol = symbol.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const symbolRegex = new RegExp(escapedSymbol, 'g');
    cleaned = cleaned.replace(symbolRegex, '').trim();
  }
  
  // Extract the numeric part (digits, separators)
  // This regex captures numbers with various separators
  const numericMatch = cleaned.match(/[\d\s,.']+/);
  
  if (!numericMatch) {
    return null;
  }
  
  return numericMatch[0].trim();
}

function parseNumericPart(numericText: string): number | null {
  if (!numericText) {
    return null;
  }

  // Handle different separator patterns
  const patterns = [
    // Pattern 1: Comma as decimal separator (European style)
    // Examples: 1.343,88 or 1 343,88 or 1'343,88
    // But NOT 1,343 (which should be thousands separator)
    {
      regex: /^([\d\s.']+),(\d{1,2})$/,
      parse: (match: RegExpMatchArray) => {
        const integerPart = match[1].replace(/[\s.']/g, '');
        const decimalPart = match[2];
        return parseFloat(`${integerPart}.${decimalPart}`);
      }
    },
    
    // Pattern 2: Dot as decimal separator (US/UK style)
    // Examples: 1,343.88 or 1 343.88 or 1'343.88
    {
      regex: /^([\d\s,']*)\.(\d{1,3})$/,
      parse: (match: RegExpMatchArray) => {
        const integerPart = match[1].replace(/[\s,']/g, '');
        const decimalPart = match[2];
        return parseFloat(`${integerPart}.${decimalPart}`);
      }
    },
    
    // Pattern 3: No decimal places, just thousands separators
    // Examples: 1,343 or 1.343 or 1 343 or 1'343
    {
      regex: /^[\d\s,.']+$/,
      parse: (match: RegExpMatchArray) => {
        const fullMatch = match[0];
        
        // If it contains only digits, it's straightforward
        if (/^\d+$/.test(fullMatch)) {
          return parseFloat(fullMatch);
        }
        
        // Check if it looks like it has thousands separators
        const parts = fullMatch.split(/[\s,.']/);
        const lastPart = parts[parts.length - 1];

        // If there are exactly 2 parts and the last part has 1-2 digits, it's likely a decimal
        if (parts.length === 2 && lastPart.length <= 2) {
          // Likely decimal separator
          const integerPart = parts[0];
          const decimalPart = lastPart;
          return parseFloat(`${integerPart}.${decimalPart}`);
        } else {
          // Either thousands separators or no decimal places
          // Remove all separators and treat as integer
          const cleaned = fullMatch.replace(/[\s,.']/g, '');
          return parseFloat(cleaned);
        }
      }
    }
  ];

  // Try each pattern
  for (const pattern of patterns) {
    const match = numericText.match(pattern.regex);
    if (match) {
      try {
        const result = pattern.parse(match);
        if (!isNaN(result) && result >= 0) {
          return result;
        }
      } catch (error) {
        // Continue to next pattern
        continue;
      }
    }
  }

  // Fallback: try to parse as a simple number after removing all non-digit characters except the last dot/comma
  const fallbackCleaned = cleanForFallbackParsing(numericText);
  if (fallbackCleaned) {
    const fallbackResult = parseFloat(fallbackCleaned);
    if (!isNaN(fallbackResult) && fallbackResult >= 0) {
      return fallbackResult;
    }
  }

  // Special case: handle prices that might be missing decimal points
  // If we have a string of digits that looks like it could be a price in cents
  if (/^\d+$/.test(numericText)) {
    const digitsOnly = parseInt(numericText, 10);

    // If it's a reasonable price range and has more than 3 digits,
    // it might be missing decimal formatting
    if (digitsOnly >= 1000 && digitsOnly <= 999999) {
      // Try treating last 2 digits as cents
      const dollarsAndCents = digitsOnly / 100;

      // Only apply this heuristic if the result looks reasonable for a product price
      if (dollarsAndCents >= 10 && dollarsAndCents <= 9999.99) {
        console.log(`[PRICE-PARSER] Applied decimal heuristic: ${numericText} -> ${dollarsAndCents}`);
        return dollarsAndCents;
      }
    }

    // Otherwise, treat as regular integer
    return digitsOnly;
  }

  return null;
}

function cleanForFallbackParsing(text: string): string | null {
  // Find the last occurrence of a potential decimal separator
  const lastDotIndex = text.lastIndexOf('.');
  const lastCommaIndex = text.lastIndexOf(',');
  
  let decimalSeparatorIndex = -1;
  let decimalSeparator = '';
  
  if (lastDotIndex > lastCommaIndex) {
    decimalSeparatorIndex = lastDotIndex;
    decimalSeparator = '.';
  } else if (lastCommaIndex > lastDotIndex) {
    decimalSeparatorIndex = lastCommaIndex;
    decimalSeparator = ',';
  }
  
  if (decimalSeparatorIndex === -1) {
    // No decimal separator, just remove all non-digits
    return text.replace(/\D/g, '');
  }
  
  // Check if the part after the separator looks like decimals (1-3 digits)
  const afterSeparator = text.substring(decimalSeparatorIndex + 1);
  if (/^\d{1,3}$/.test(afterSeparator)) {
    // Treat as decimal separator
    const beforeSeparator = text.substring(0, decimalSeparatorIndex).replace(/\D/g, '');
    return `${beforeSeparator}.${afterSeparator}`;
  } else {
    // Treat as thousands separator, remove all non-digits
    return text.replace(/\D/g, '');
  }
}
