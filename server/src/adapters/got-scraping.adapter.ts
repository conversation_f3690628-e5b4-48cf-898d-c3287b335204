import { gotScraping } from "got-scraping";
import * as cheerio from "cheerio";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rape<PERSON><PERSON>ult } from "./types";
import { parsePrice } from "../utils/price-parser";

export const gotScrapingAdapter: ScraperAdapter = {
  name: 'gotScraping',
  async scrape(url: string, selector: string): Promise<ScrapeResult> {
    try {
      // Use got-scraping with minimal configuration to leverage its anti-detection features
      const response = await gotScraping.get(url, {
        timeout: { request: 15000 } // Only increase timeout, let got-scraping handle the rest
      });

      const html = response.body;
      const $ = cheerio.load(html);

      const priceElement = $(selector);
      if (priceElement.length === 0) {
        // Return null price but no error - this allows fallback to Playwright
        return { price: null };
      }

      const priceText = priceElement.first().text().trim();
      console.log(`[GOT-SCRAPING] Raw price text extracted: "${priceText}"`);

      const price = parsePrice(priceText);
      console.log(`[GOT-SCRAPING] Parsed price result: "${price}"`);

      if (!price) {
        return { price: null, error: `Could not extract a valid price from text: "${priceText}"` };
      }

      return { price };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.log(`[GOT-SCRAPING] Failed for ${url}: ${errorMessage}`);
      return { price: null, error: errorMessage };
    }
  }
};
