// Debug script to test price parsing with the problematic Best Buy case

const { parsePrice } = require('./server/src/utils/price-parser');

// Test cases that might be extracted from Best Buy
const testCases = [
  '134388',      // What we're getting (missing decimal)
  '$134388',     // With currency symbol
  '1343.88',     // What we expect
  '$1343.88',    // With currency and decimal
  '1,343.88',    // With thousands separator
  '$1,343.88',   // Full format
  '134388 CAD',  // With currency code
  '1343',        // Without decimals
  '13438',       // 5 digits
  '134388',      // 6 digits (our problem case)
];

console.log('Testing price parsing with various formats:');
console.log('='.repeat(50));

testCases.forEach(testCase => {
  const result = parsePrice(testCase);
  console.log(`Input: "${testCase}" -> Output: "${result}"`);
});

console.log('\n' + '='.repeat(50));
console.log('Expected: 134388 should parse to 1343.88');
console.log('Actual result:', parsePrice('134388'));
