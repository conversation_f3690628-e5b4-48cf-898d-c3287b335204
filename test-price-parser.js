// Test the price parser with the exact input from Best Buy
import { parsePrice } from './server/src/utils/price-parser.js';

const testInput = "$1,343.88";
console.log(`Testing with exact input: "${testInput}"`);
console.log(`Result: "${parsePrice(testInput)}"`);

// Let's also test step by step to see what's happening
console.log('\nDebugging step by step:');

// Test the extractNumericPart function manually
function extractNumericPart(text) {
  const CURRENCY_CODES = ['USD', 'EUR', 'GBP', 'JPY', 'INR', 'RUB', 'KRW', 'ILS', 'NGN', 'CRC', 'PKR', 'GHS', 'UAH', 'KZT', 'AZN', 'GEL', 'BTC'];
  const CURRENCY_SYMBOLS = ['$', '€', '£', '¥', '₹', '₽', '₩', '₪', '₦', '₡', '₨', '₵', '₴', '₸', '₼', '₾', '₿'];
  
  let cleaned = text;
  
  // Remove currency codes
  for (const code of CURRENCY_CODES) {
    const codeRegex = new RegExp(`\\b${code}\\b`, 'gi');
    cleaned = cleaned.replace(codeRegex, '').trim();
  }
  
  // Remove currency symbols
  for (const symbol of CURRENCY_SYMBOLS) {
    const escapedSymbol = symbol.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const symbolRegex = new RegExp(escapedSymbol, 'g');
    cleaned = cleaned.replace(symbolRegex, '').trim();
  }
  
  // Extract the numeric part
  const numericMatch = cleaned.match(/[\d\s,.']+/);
  
  return numericMatch ? numericMatch[0].trim() : null;
}

const numericPart = extractNumericPart(testInput);
console.log(`Numeric part extracted: "${numericPart}"`);

// Test the parseNumericPart function
function testParseNumericPart(numericText) {
  console.log(`\nTesting parseNumericPart with: "${numericText}"`);
  
  // Pattern 2: Dot as decimal separator (US/UK style)
  const pattern2Regex = /^([\d\s,']*)\.(\d{1,3})$/;
  const match = numericText.match(pattern2Regex);
  
  if (match) {
    console.log(`Pattern 2 matched:`, match);
    const integerPart = match[1].replace(/[\s,']/g, '');
    const decimalPart = match[2];
    const result = parseFloat(`${integerPart}.${decimalPart}`);
    console.log(`Integer part: "${integerPart}", Decimal part: "${decimalPart}"`);
    console.log(`Final result: ${result}`);
    return result;
  } else {
    console.log('Pattern 2 did not match');
    return null;
  }
}

if (numericPart) {
  testParseNumericPart(numericPart);
}
