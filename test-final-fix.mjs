// Final test to verify the fix works end-to-end
import { chromium } from 'playwright';
import * as cheerio from 'cheerio';
import gotScraping from 'got-scraping';

// Import the actual price parser (simplified version)
const CURRENCY_SYMBOLS = ['$', '€', '£', '¥', '₹', '₽', '₩', '₪', '₦', '₡', '₨', '₵', '₴', '₸', '₼', '₾', '₿'];
const CURRENCY_CODES = ['USD', 'EUR', 'GBP', 'JPY', 'INR', 'RUB', 'KRW', 'ILS', 'NGN', 'CRC', 'PKR', 'GHS', 'UAH', 'KZT', 'AZN', 'GEL', 'BTC'];

function extractNumericPart(text) {
  let cleaned = text;
  
  for (const code of CURRENCY_CODES) {
    const codeRegex = new RegExp(`\\b${code}\\b`, 'gi');
    cleaned = cleaned.replace(codeRegex, '').trim();
  }
  
  for (const symbol of CURRENCY_SYMBOLS) {
    const escapedSymbol = symbol.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const symbolRegex = new RegExp(escapedSymbol, 'g');
    cleaned = cleaned.replace(symbolRegex, '').trim();
  }
  
  const numericMatch = cleaned.match(/[\d\s,.']+/);
  return numericMatch ? numericMatch[0].trim() : null;
}

function parseNumericPart(numericText) {
  if (!numericText) return null;

  // Pattern 2: Dot as decimal separator (US/UK style)
  const pattern2Regex = /^([\d\s,']*)\.(\d{1,3})$/;
  const match = numericText.match(pattern2Regex);
  
  if (match) {
    const integerPart = match[1].replace(/[\s,']/g, '');
    const decimalPart = match[2];
    return parseFloat(`${integerPart}.${decimalPart}`);
  }
  
  // Fallback for other patterns
  if (/^\d+$/.test(numericText)) {
    return parseFloat(numericText);
  }
  
  return parseFloat(numericText.replace(/[\s,']/g, ''));
}

function parsePrice(priceText) {
  if (!priceText || typeof priceText !== 'string') return null;
  
  let cleanText = priceText.trim().replace(/\s+/g, ' ');
  if (!cleanText) return null;

  const numericPart = extractNumericPart(cleanText);
  if (!numericPart) return null;

  const parsedNumber = parseNumericPart(numericPart);
  if (parsedNumber === null || isNaN(parsedNumber) || parsedNumber < 0) return null;

  return parsedNumber.toString();
}

const url = 'https://www.bestbuy.ca/en-ca/product/asus-vivobook-s-15-laptop-intel-16-core-ultra-7-155h-15-6-3k-600nits-display-intel-arc-graphics-16gb-1tb-ssd-backlit-keyboard-thunderbolt-4-wifi-6e-hdmi-2-1-win11-home/19392445';
const selector = '[data-automation="option-box-price"]';

console.log('Testing final fix end-to-end...');
console.log('Expected result: Price should be 1343.88 (not 134388)');
console.log('='.repeat(60));

// Test with Playwright adapter (like the actual scraper)
async function testPlaywrightAdapter() {
  console.log('\n1. Testing Playwright Adapter:');
  
  let browser;
  try {
    browser = await chromium.launch({ headless: true });
    const page = await browser.newPage({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      viewport: { width: 1280, height: 720 }
    });

    await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 20000 });
    await page.waitForTimeout(2000);

    const html = await page.content();
    const $ = cheerio.load(html);

    const priceElement = $(selector);
    if (priceElement.length === 0) {
      console.log('   ❌ Selector not found');
      return null;
    }

    const priceText = priceElement.first().text().trim();
    console.log(`   Raw text: "${priceText}"`);
    
    const price = parsePrice(priceText);
    console.log(`   Parsed price: "${price}"`);
    
    if (price === '1343.88') {
      console.log('   ✅ SUCCESS: Price correctly parsed as 1343.88');
    } else {
      console.log(`   ❌ FAILED: Expected 1343.88, got ${price}`);
    }
    
    return price;

  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return null;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Test with got-scraping adapter
async function testGotScrapingAdapter() {
  console.log('\n2. Testing Got-Scraping Adapter:');
  
  try {
    const response = await gotScraping.get(url, {
      timeout: { request: 15000 }
    });

    const html = response.body;
    const $ = cheerio.load(html);

    const priceElement = $(selector);
    if (priceElement.length === 0) {
      console.log('   ❌ Selector not found');
      return null;
    }

    const priceText = priceElement.first().text().trim();
    console.log(`   Raw text: "${priceText}"`);
    
    const price = parsePrice(priceText);
    console.log(`   Parsed price: "${price}"`);
    
    if (price === '1343.88') {
      console.log('   ✅ SUCCESS: Price correctly parsed as 1343.88');
    } else {
      console.log(`   ❌ FAILED: Expected 1343.88, got ${price}`);
    }
    
    return price;

  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return null;
  }
}

// Run both tests
const playwrightResult = await testPlaywrightAdapter();
const gotScrapingResult = await testGotScrapingAdapter();

console.log('\n' + '='.repeat(60));
console.log('SUMMARY:');
console.log(`Playwright result: ${playwrightResult}`);
console.log(`Got-scraping result: ${gotScrapingResult}`);

if (playwrightResult === '1343.88' || gotScrapingResult === '1343.88') {
  console.log('✅ FIX SUCCESSFUL: At least one adapter now correctly extracts 1343.88');
} else {
  console.log('❌ FIX FAILED: Neither adapter extracted the correct price');
}

console.log('\nDone!');
